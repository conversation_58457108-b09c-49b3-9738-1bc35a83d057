WITH
	e1 AS (
		SELECT DISTINCT
			json_extract_scalar (raw_data, '$.distinct_id') AS distinct_id
		FROM
			hungry_studio.ods_ab_fenliu_result_gp_hi
		WHERE
			dt = '2025-06-07'
			AND json_extract_scalar(raw_data, '$.exp_id') = '529'
	),
	e2 AS (
		SELECT DISTINCT
			json_extract_scalar (raw_data, '$.distinct_id') AS distinct_id
		FROM
			hungry_studio.ods_ab_fenliu_result_gp_hi
		WHERE
			dt = '2025-06-07'
			AND json_extract_scalar (raw_data, '$.exp_id') = '530'
	),
	counts AS (
		SELECT
			(SELECT COUNT(distinct_id) FROM e1) AS count_e1,
			(SELECT COUNT(distinct_id) FROM e2) AS count_e2,
			(
				SELECT COUNT(t1.distinct_id)
				FROM e1 t1
				INNER JOIN e2 t2 ON t1.distinct_id = t2.distinct_id
			) AS count_overlap
	)
SELECT
	count_e1,
	count_e2,
	count_overlap,
	CASE
		WHEN count_e1 > 0 THEN CAST(count_overlap AS REAL) * 100.0 / count_e1
		ELSE 0.0
	END AS overlap_rate_e1_percent, -- 交集用户占 e1 用户的百分比
	CASE
		WHEN count_e2 > 0 THEN CAST(count_overlap AS REAL) * 100.0 / count_e2
		ELSE 0.0
	END AS overlap_rate_e2_percent, -- 交集用户占 e2 用户的百分比
	CASE
		WHEN (count_e1 + count_e2 - count_overlap) > 0 THEN CAST(count_overlap AS REAL) * 100.0 / (count_e1 + count_e2 - count_overlap)
		ELSE 0.0
	END AS jaccard_index_percent      -- Jaccard 相似系数 (百分比)
FROM
	counts;