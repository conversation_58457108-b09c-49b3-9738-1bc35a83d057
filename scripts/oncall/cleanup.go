package oncall

import (
	"context"
	"fmt"
	"log/slog"
	"time"

	"git.7k7k.com/data/abScheduler/infra"
	"github.com/redis/go-redis/v9"
)

// CleanupIdleKeys 删除 OBJECT IDLETIME 超过指定天数的 key。
// days 指定空闲天数阈值，例如 30 表示删除空闲 ≥30 天的 key。
// cli / batch / pattern 等参数保持与 AnalysisIDLETIME 一致，方便在 runcode 中调用。
func CleanupIdleKeys(ctx context.Context, app *infra.Application, days int) {
	cli := app.RedisMgr.UserStateRedis
	// 不限制处理数量，只要游标未归零就持续扫描删除
	batch := int64(1000) // SCAN 命令的 COUNT 参数
	pattern := "uste:*"

	slog.Info("开始清理 Redis Key", "threshold_days", days, "pattern", pattern, "batch_size", batch)

	const oneDaySeconds = 24 * 60 * 60
	thresholdSeconds := int64(days) * oneDaySeconds

	var cursor uint64
	cursor = 0
	processed := 0
	deleted := 0

	start := time.Now()

	for {

		keys, nextCursor, err := cli.<PERSON>an(ctx, cursor, pattern, batch).Result()
		if err != nil {
			slog.Error("Redis SCAN 失败", "cursor", cursor, "err", err)
			break
		}
		cursor = nextCursor

		if len(keys) == 0 && cursor == 0 {
			slog.Info("SCAN 完成 (空结果且 cursor=0)")
			break
		}

		// pipeline 查询 idletime
		pipe := cli.Pipeline()
		cmds := make([]*redis.DurationCmd, 0, len(keys))
		for _, k := range keys {
			cmds = append(cmds, pipe.ObjectIdleTime(ctx, k))
		}
		_, execErr := pipe.Exec(ctx)

		// 第二个 pipeline 用于批量删除
		delPipe := cli.Pipeline()
		needDel := 0

		for i, k := range keys {

			var idle time.Duration
			var cmdErr error
			if execErr != nil {
				cmdErr = execErr
			} else {
				idle, cmdErr = cmds[i].Result()
			}

			if cmdErr != nil {
				if cmdErr != redis.Nil {
					slog.Warn("获取 IDLETIME 失败", "key", k, "err", cmdErr.Error())
				}
				// redis.Nil 直接忽略
			} else if int64(idle.Seconds()) >= thresholdSeconds {
				// 删除
				delPipe.Del(ctx, k)
				needDel++
				deleted++
				if deleted%10000 == 0 {
					slog.Info("已删除1万条", "deleted", deleted, "cursor", cursor, "key", k)

					time.Sleep(time.Second)
				}
			}
			processed++
		}

		if needDel > 0 {
			if _, err := delPipe.Exec(ctx); err != nil {
				slog.Error("批量删除 key 失败", "err", err)
			}
		}

		if cursor == 0 {
			slog.Info("SCAN 完成 (cursor=0)")
			break
		}
	}

	duration := time.Since(start)

	// 输出结果
	fmt.Println("\n====================== Redis Key 清理结果 ======================")
	fmt.Printf("扫描模式: %s\n", pattern)
	fmt.Printf("阈值(天): %d\n", days)
	fmt.Printf("已扫描: %d, 删除数量: %d\n", processed, deleted)
	fmt.Printf("耗时: %s\n", duration.String())

	fmt.Println("===============================================================")

	slog.Info("清理完成", "threshold_days", days, "processed", processed, "deleted", deleted, "duration", duration.String())
}

// CleanupOver30Days 删除 OBJECT IDLETIME 超过 30 天的 key。
// 参数设计（cli / N / batch / pattern）与 AnalysisIDLETIME 保持一致，方便在 runcode 中替换调用。
func CleanupOver30Days(ctx context.Context, app *infra.Application) {
	CleanupIdleKeys(ctx, app, 3)
}
