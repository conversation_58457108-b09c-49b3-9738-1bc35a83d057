package oncall

import (
	"context"
	"fmt"
	"log/slog"
	"os"
	"time" // 新增

	"git.7k7k.com/data/abScheduler/infra"
	"git.7k7k.com/data/abScheduler/repository/cache" // 保留
	"github.com/redis/go-redis/v9"                   // 新增
)

func AnalysisAllProjects(ctx context.Context, app *infra.Application) {
	// 最多扫描100万个key
	stats, err := cache.AnalysisAllProjects(ctx, app.RedisMgr.UserStateRedis, 100_0000)
	if err != nil {
		slog.Error("AnalysisAllProjects", "err", err)
		return
	}

	// 收集所有项目ID
	projectIDs := make([]int, 0, len(stats))
	for pid := range stats {
		projectIDs = append(projectIDs, pid)
	}

	// 默认按内存使用量从大到小排序
	sortByMem := true
	if os.Getenv("SORT_BY_ID") == "1" {
		sortByMem = false
	}

	if sortByMem {
		// 按内存使用量从大到小排序
		for i := 0; i < len(projectIDs); i++ {
			for j := i + 1; j < len(projectIDs); j++ {
				if stats[projectIDs[i]].TotalBytes < stats[projectIDs[j]].TotalBytes {
					projectIDs[i], projectIDs[j] = projectIDs[j], projectIDs[i]
				}
			}
		}
	} else {
		// 按项目ID排序
		for i := 0; i < len(projectIDs); i++ {
			for j := i + 1; j < len(projectIDs); j++ {
				if projectIDs[i] > projectIDs[j] {
					projectIDs[i], projectIDs[j] = projectIDs[j], projectIDs[i]
				}
			}
		}
	}

	// 输出每个项目的统计信息
	fmt.Println("\n====================== Redis 内存占用统计 ======================")
	fmt.Printf("%-10s %-15s %-20s %-15s %-15s\n", "项目ID", "Key数量", "总内存占用(MB)", "占比(%)", "平均每Key(KB)")
	fmt.Println("----------------------------------------------------------------")

	var totalKeys int64
	var totalBytes int64
	for _, pid := range projectIDs {
		stat := stats[pid]
		totalKeys += stat.KeyCount
		totalBytes += stat.TotalBytes
	}

	// 先输出排序后的项目统计
	for _, pid := range projectIDs {
		stat := stats[pid]
		percentage := 0.0
		if totalBytes > 0 {
			percentage = float64(stat.TotalBytes) / float64(totalBytes) * 100
		}

		fmt.Printf("%-10d %-15d %-20.2f %-15.2f %-15.2f\n",
			stat.ProjectID,
			stat.KeyCount,
			float64(stat.TotalBytes)/(1024*1024), // MB
			percentage,                           // 占总内存百分比
			stat.AvgKeyBytes/1024)                // KB
	}

	// 输出总计信息
	avgBytes := 0.0
	if totalKeys > 0 {
		avgBytes = float64(totalBytes) / float64(totalKeys) / 1024
	}

	fmt.Println("----------------------------------------------------------------")
	fmt.Printf("%-10s %-15d %-20.2f %-15s %-15.2f\n",
		"总计",
		totalKeys,
		float64(totalBytes)/(1024*1024), // MB
		"100.00",                        // 占比100%
		avgBytes)                        // KB
	fmt.Println("================================================================")

	// 输出排序方式说明
	if sortByMem {
		fmt.Println("* 项目按内存使用量从大到小排序，使用 SORT_BY_ID=1 可改为按项目ID排序")
	} else {
		fmt.Println("* 项目按ID排序，去掉 SORT_BY_ID=1 可改为按内存使用量排序")
	}
	fmt.Println()

	// 同时保留日志输出
	for _, stat := range stats {
		slog.Info(fmt.Sprintf("项目 %d 内存使用统计:", stat.ProjectID),
			"Key数量", fmt.Sprintf("%d", stat.KeyCount),
			"总内存", fmt.Sprintf("%.2f MB", float64(stat.TotalBytes)/(1024*1024)),
			"占比", fmt.Sprintf("%.2f%%", float64(stat.TotalBytes)/float64(totalBytes)*100),
			"平均每Key", fmt.Sprintf("%.2f KB", stat.AvgKeyBytes/1024))
	}
}

// AnalysisIDLETIME 统计 IDLETIME 的分布
// 在redis里scan 出100万个以"uste:"开头的key，检查每个key已经多久没访问了，然后统计一下分布
// ====================== Redis Key IDLETIME 分布统计 ======================
// 扫描 Key 模式: uste:*
// 目标最大处理 Key 数量: 2000000
// 实际处理 Key 数量: 2000000
// Redis SCAN 命令返回 Key 总数 (估算): 2000439
// 分析耗时: 6.173137111s
// --------------------------------------------------------------------------
// IDLETIME 区间     | Key 数量
// --------------------------------------------------------------------------
// < 1 day         | 1346024
// 1-7 days        | 384518
// 7-30 days       | 208755
// >= 30 days      | 60703
// error/nil       | 0
// --------------------------------------------------------------------------
// 总计 (已处理)        | 2000000
// ==========================================================================
func AnalysisIDLETIME(ctx context.Context, app *infra.Application) {
	cli := app.RedisMgr.UserStateRedis
	N := 1000000         // 最多扫描 N 个 key, 按照注释修改为100万
	batch := int64(1000) // SCAN 命令的 COUNT 参数
	pattern := "uste:*"

	slog.Info("开始分析 Redis Key IDLETIME", "max_keys_to_process", N, "pattern", pattern, "batch_size", batch)

	var cursor uint64
	keysScannedFromRedis := 0              // 记录从 Redis SCAN 命令实际返回的 key 的总数
	processedKeysCount := 0                // 记录已成功获取并分类 IDLETIME 的 key 的数量, 此值不应超过 N
	idleTimeCounts := make(map[string]int) // 用于存储不同空闲时间区间的key数量

	// 定义空闲时间区间 (单位：秒)
	// Redis的IDLETIME返回的是秒数
	const (
		oneDaySeconds   = 24 * 60 * 60
		oneWeekSeconds  = 7 * oneDaySeconds
		oneMonthSeconds = 30 * oneDaySeconds
	)

	// 定义统计区间
	idleBuckets := []struct {
		name  string
		lower int64 // seconds, inclusive lower bound
		upper int64 // seconds, inclusive upper bound. -1 for infinity in the last bucket.
	}{
		{"< 1 day", 0, oneDaySeconds - 1},
		{"1-3 days", oneDaySeconds, 3*oneDaySeconds - 1},
		{"3-7 days", 3 * oneDaySeconds, oneWeekSeconds - 1},
		{"7-30 days", oneWeekSeconds, oneMonthSeconds - 1},
		{">= 30 days", oneMonthSeconds, -1}, // -1 表示没有上限
		{"error/nil", -2, -2},               // 特殊标记，用于记录获取失败或IDLETIME为nil的情况
	}
	// 初始化计数器
	for _, bucket := range idleBuckets {
		idleTimeCounts[bucket.name] = 0
	}

	// 采样存储超过30天的key，最多记录10条
	sampleKeysOver30Days := make([]string, 0, 10)

	startTime := time.Now()

	for {
		if processedKeysCount >= N {
			slog.Info("已达到最大处理数量限制", "processed_keys", processedKeysCount, "limit", N)
			break
		}

		// 执行 SCAN 命令
		keysThisScan, nextCursor, err := cli.Scan(ctx, cursor, pattern, batch).Result()
		if err != nil {
			slog.Error("Redis SCAN 命令失败", "cursor", cursor, "err", err)
			break // 发生严重错误则停止扫描
		}
		cursor = nextCursor // 更新游标以供下一次迭代使用
		keysScannedFromRedis += len(keysThisScan)

		if len(keysThisScan) == 0 && cursor == 0 {
			slog.Info("已扫描完所有匹配的key (SCAN返回空结果且cursor为0)")
			break
		}

		keysToProcessThisBatch := keysThisScan
		// 如果当前批次加上已处理的会导致超过N，则截断当前批次
		if processedKeysCount+len(keysThisScan) > N {
			needed := N - processedKeysCount
			if needed < 0 {
				needed = 0
			} // 以防万一
			keysToProcessThisBatch = keysThisScan[:needed]
		}

		if len(keysToProcessThisBatch) == 0 {
			// 如果截断后没有key了，并且之前已经处理满了N个，或者游标为0（说明确实没key了）
			if processedKeysCount >= N || cursor == 0 {
				slog.Info("当前批次无需处理或已扫描完毕", "processed_keys", processedKeysCount, "cursor", cursor)
				break
			}
			// 如果只是当前批次被截断完，但还没处理满N个且游标不为0，则继续scan
			slog.Info("当前批次被截断后无key可处理，继续下一轮SCAN", "next_cursor", cursor)
			if cursor == 0 {
				break
			} // 如果游标也为0，则结束
			continue
		}

		// 批量获取 IDLETIME
		pipe := cli.Pipeline()
		idleCmds := make([]*redis.DurationCmd, 0, len(keysToProcessThisBatch))
		for _, key := range keysToProcessThisBatch {
			idleCmds = append(idleCmds, pipe.ObjectIdleTime(ctx, key))
		}

		// 执行pipeline。pipe.Exec() 本身也可能返回错误，代表pipeline层面问题
		_, execErr := pipe.Exec(ctx)

		for i, key := range keysToProcessThisBatch {
			// 检查是否已处理足够数量的key
			if processedKeysCount >= N {
				break // 已达到处理上限，跳出内层循环
			}

			var idleDuration time.Duration
			var cmdErr error

			if execErr != nil {
				// 如果 pipeline 执行失败，则所有命令都视为失败
				cmdErr = fmt.Errorf("pipeline exec error: %w", execErr)
			} else {
				// 获取单个命令的结果
				idleDuration, cmdErr = idleCmds[i].Result()
			}

			if cmdErr != nil {
				if cmdErr == redis.Nil { // redis.Nil 表示 key 不存在或 OBJECT IDLETIME 不适用
					slog.Debug("获取 Key IDLETIME 返回 redis.Nil (key可能不存在或已过期)", "key", key)
				} else {
					slog.Warn("获取 Key IDLETIME 失败", "key", key, "err", cmdErr.Error())
				}
				idleTimeCounts["error/nil"]++
			} else {
				idleSeconds := int64(idleDuration.Seconds())
				foundBucket := false
				for _, bucket := range idleBuckets {
					if bucket.name == "error/nil" { // 跳过特殊标记桶的常规比较
						continue
					}
					// 检查是否在当前桶的范围内
					if idleSeconds >= bucket.lower && (bucket.upper == -1 || idleSeconds <= bucket.upper) {
						idleTimeCounts[bucket.name]++
						if bucket.name == ">= 30 days" && len(sampleKeysOver30Days) < 10 {
							sampleKeysOver30Days = append(sampleKeysOver30Days, key)
						}
						foundBucket = true
						break
					}
				}
				if !foundBucket { // 如果没有落入任何预定义区间 (例如idleSeconds为负数)
					slog.Warn("未找到合适的 IDLETIME 区间 (可能为负数或异常值)", "key", key, "idle_seconds", idleSeconds)
					idleTimeCounts["error/nil"]++ // 归入error/nil类别
				}
			}
			processedKeysCount++ // 增加已处理key的计数
		}

		// slog.Info("处理完一批key",
		// 	"keys_in_scan_batch", len(keysThisScan),
		// 	"keys_processed_in_this_batch", len(keysToProcessThisBatch),
		// 	"total_processed_so_far", processedKeysCount,
		// 	"next_cursor", cursor)

		if cursor == 0 { // 如果 SCAN 返回的游标为0，表示所有匹配的key都已被迭代完毕
			slog.Info("已扫描完所有匹配的key (cursor is 0 after batch processing)")
			break
		}
	}

	duration := time.Since(startTime)
	slog.Info("IDLETIME 分析完成", "total_duration", duration.String(), "total_keys_processed", processedKeysCount, "total_keys_scanned_from_redis", keysScannedFromRedis)

	// 输出统计结果
	fmt.Println("\n====================== Redis Key IDLETIME 分布统计 ======================")
	fmt.Printf("扫描 Key 模式: %s\n", pattern)
	fmt.Printf("目标最大处理 Key 数量: %d\n", N)
	fmt.Printf("实际处理 Key 数量: %d\n", processedKeysCount)
	fmt.Printf("Redis SCAN 命令返回 Key 总数 (估算): %d\n", keysScannedFromRedis)
	fmt.Printf("分析耗时: %s\n", duration.String())
	fmt.Println("--------------------------------------------------------------------------")
	fmt.Printf("%-15s | %-10s\n", "IDLETIME 区间", "Key 数量")
	fmt.Println("--------------------------------------------------------------------------")

	// 按照定义的 bucket 顺序打印，确保输出的一致性
	bucketOrder := []string{"< 1 day", "1-3 days", "3-7 days", "7-30 days", ">= 30 days", "error/nil"}
	for _, bucketName := range bucketOrder {
		count := idleTimeCounts[bucketName]
		fmt.Printf("%-15s | %-10d\n", bucketName, count)
	}

	// 如果采样到了超过30天的key，则打印示例
	if len(sampleKeysOver30Days) > 0 {
		fmt.Println("\n超过30天的示例Key（最多10条）：")
		for _, k := range sampleKeysOver30Days {
			fmt.Println(k)
		}
	}

	fmt.Println("--------------------------------------------------------------------------")
	fmt.Printf("%-15s | %-10d\n", "总计 (已处理)", processedKeysCount)
	fmt.Println("==========================================================================")
	fmt.Println()
}
