package oncall

import (
	"context"
	"fmt"
	"log/slog"
	"time"

	"github.com/redis/go-redis/v9"
)

// MoveUserState 从 redis1 复制匹配的 key 到 redis2
// 1. 使用 SCAN 每次扫描 batchSize (默认 1000) 个 key；
// 2. 对每批 key 通过 pipeline 批量 DUMP + PTTL 获取序列化值和剩余 TTL；
// 3. 再在 redis2 使用 RESTORE REPLACE 还原数据；
// 4. redis1 中的数据不做删除。
// 复制过程中会输出日志，方便监控进度。
func MoveUserState(ctx context.Context, redis1 redis.UniversalClient, redis2 redis.UniversalClient) {
	const (
		batchSize      = int64(1000) // SCAN COUNT
		pattern        = "uste:10:*" // 需要迁移的 key 模式
		logEveryCopied = 10_000      // 每复制多少条打印一次进度
	)

	slog.Info("开始迁移 Redis Key", "pattern", pattern, "batch_size", batchSize)

	var (
		cursor    uint64
		processed int
		copied    int
	)

	start := time.Now()

	for {
		// 从 redis1 扫描 key
		keys, nextCursor, err := redis1.Scan(ctx, cursor, pattern, batchSize).Result()
		if err != nil {
			slog.Error("Redis SCAN 失败", "cursor", cursor, "err", err)
			break
		}
		cursor = nextCursor

		if len(keys) == 0 && cursor == 0 {
			slog.Info("SCAN 完成 (空结果且 cursor=0)")
			break
		}
		if len(keys) == 0 {
			if cursor == 0 {
				break
			}
			continue
		}

		// pipeline 获取 DUMP 与 PTTL
		pipe := redis1.Pipeline()
		dumpCmds := make([]*redis.StringCmd, len(keys))
		ttlCmds := make([]*redis.DurationCmd, len(keys))
		for i, k := range keys {
			dumpCmds[i] = pipe.Dump(ctx, k)
			ttlCmds[i] = pipe.PTTL(ctx, k)
		}
		// 执行 pipeline
		_, execErr := pipe.Exec(ctx)

		// 第二个 pipeline 用于在 redis2 RESTORE
		restorePipe := redis2.Pipeline()
		needRestore := 0

		for i, k := range keys {
			if execErr != nil {
				slog.Warn("pipeline 获取 DUMP/PTTL 失败", "key", k, "err", execErr)
				processed++
				continue
			}

			dumpStr, dumpErr := dumpCmds[i].Result()
			ttlDur, ttlErr := ttlCmds[i].Result()

			// 如果 key 在这一步骤被删除，DUMP 会返回 redis.Nil
			if dumpErr != nil {
				if dumpErr != redis.Nil {
					slog.Warn("DUMP 失败", "key", k, "err", dumpErr)
				}
				processed++
				continue
			}
			if ttlErr != nil && ttlErr != redis.Nil {
				slog.Warn("PTTL 失败", "key", k, "err", ttlErr)
			}

			// PTTL 为 -1 表示永久，或 -2 表示 key 不存在
			if ttlErr == redis.Nil || ttlDur < 0 {
				ttlDur = 0
			}

			restorePipe.RestoreReplace(ctx, k, ttlDur, dumpStr)
			needRestore++
			copied++
			processed++

			if copied%logEveryCopied == 0 {
				slog.Info("已迁移 1 万条", "copied", copied, "cursor", cursor, "last_key", k)

				time.Sleep(time.Second)
			}
		}

		if needRestore > 0 {
			if _, err := restorePipe.Exec(ctx); err != nil {
				slog.Error("批量 RESTORE 失败", "err", err)
			}
		}

		if cursor == 0 {
			slog.Info("迁移完成 (cursor=0)")
			break
		}
	}

	duration := time.Since(start)

	// 输出统计结果
	fmt.Println("\n====================== Redis Key 迁移结果 ======================")
	fmt.Printf("扫描模式: %s\n", pattern)
	fmt.Printf("已扫描: %d, 成功复制: %d\n", processed, copied)
	fmt.Printf("耗时: %s\n", duration.String())
	fmt.Println("===============================================================")

	slog.Info("迁移结束", "processed", processed, "copied", copied, "duration", duration.String())
}
