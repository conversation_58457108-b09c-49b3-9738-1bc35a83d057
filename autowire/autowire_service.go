// Code generated by go-autowire. DO NOT EDIT.

//go:build wireinject
// +build wireinject

package autowire

import (
	"github.com/google/wire"

	"git.7k7k.com/data/abScheduler/service"
	"git.7k7k.com/data/abScheduler/service/abtest"
)

var ServiceSet = wire.NewSet(
	wire.Struct(new(service.AutoRedirectService), "*"),

	wire.Struct(new(service.CleanService), "*"),

	wire.Struct(new(service.DataTransfer), "*"),

	wire.Struct(new(service.DebugService), "*"),

	wire.Struct(new(service.HashService), "*"),

	wire.Struct(new(service.MetricService), "*"),

	service.NewConfigMetaStore,

	service.NewConfigService,

	service.NewGroupService,

	service.NewUserHistoryService,

	service.NewUserStateService,

	service.NewWhiteCombService,

	wire.Struct(new(service.RedirectService), "*"),

	wire.Struct(new(service.UserLabelService), "*"),

	wire.Struct(new(service.UserLeaveService), "*"),

	wire.Struct(new(abtest.DistributeService), "*"),
)
