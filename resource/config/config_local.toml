addr = ":7020"
mode = "local"

[sentry]
env = "local"
dsn = "http://a170683951dfb59658a561a52cf7f3d0@106.75.65.198:9000/3"
traces_sample_rate = 1

[database]
mysql_default = "abuser"

[database.mysql.abuser]
dsn = "xxgame_abtest_slt:CJy9p+lh7s4Wc6PG@tcp(106.75.29.167:23306)/abtest_user?charset=utf8mb4&parseTime=True&loc=Local&timeout=3s&readTimeout=5s"
debug = true

[database.mysql.admindb]
dsn = "xxgame_abtest_slt:CJy9p+lh7s4Wc6PG@tcp(106.75.29.167:23306)/abtest_dev?charset=utf8mb4&parseTime=True&loc=Local&timeout=3s&readTimeout=5s"
debug = true

[database.mysql.admindb.read]
dsn = "xxgame_abtest_slt:CJy9p+lh7s4Wc6PG@tcp(106.75.29.167:23306)/abtest_dev?charset=utf8mb4&parseTime=True&loc=Local&timeout=3s&readTimeout=5s"
debug = true

[database.redis.default]
addr = "106.75.65.198:6379"
db = 15

[database.redis.default_v2]
addr = "106.75.65.198:6379"
db = 15

[database.redis.ab_config]	# 实验配置
addr = "106.75.65.198:6379"
db = 15

[database.redis.user_state]	# 用户分桶状态
addr = "106.75.65.198:6666"
db = 15

[database.redis.user_state_tier]	# 用户分桶状态
addr = "106.75.65.198:6666"
db = 15

[database.redis.user_label]	# 用户标签
addr = "106.75.65.198:6379"
db = 15

[database.redis.admin]	# 给 admin 后台使用的数据
addr = "106.75.65.198:6379"
db = 4

[database.mongodb.abtest]
uri = "****************************************************************************"
db = "abtest"

[database.mongodb.abtest_unit_testing]
uri = "****************************************************************************"
db = "abtest_unit_testing"

[mq.kafka.abtest]
addrs = ["106.75.65.198:9092"]

[mq.kafka.abtest_state]
addrs = ["106.75.65.198:9092"]
