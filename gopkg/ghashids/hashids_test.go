package ghashids

import (
	"testing"
)

func TestEncodeAndDecodeSingleID(t *testing.T) {
	originalIDs := []int{123}
	hashid, err := EncodeIDs(originalIDs)
	if err != nil {
		t.Fatalf("EncodeIDs failed: %v", err)
	}
	if len(hashid) < 10 {
		t.<PERSON><PERSON><PERSON>("hashid length is less than 10: %s", hashid)
	}
	decodedIDs, err := DecodeHashIDs([]string{hashid})
	if err != nil {
		t.Fatalf("DecodeHashIDs failed: %v", err)
	}
	if len(decodedIDs) != 1 || decodedIDs[0] != originalIDs[0] {
		t.<PERSON><PERSON>rf("Decoded IDs mismatch: got %v, want %v", decodedIDs, originalIDs)
	}
}

func TestEncodeAndDecodeMultipleIDs(t *testing.T) {
	originalIDs := []int{1, 2, 3}
	hashid, err := EncodeIDs(originalIDs)
	if err != nil {
		t.Fatalf("EncodeIDs failed: %v", err)
	}
	decodedIDs, err := DecodeHashIDs([]string{hashid})
	if err != nil {
		t.Fatalf("DecodeHashIDs failed: %v", err)
	}
	if len(decodedIDs) != len(originalIDs) {
		t.Fatalf("Decoded length mismatch: got %d, want %d", len(decodedIDs), len(originalIDs))
	}
	for i, id := range decodedIDs {
		if id != originalIDs[i] {
			t.Errorf("ID mismatch at index %d: got %d, want %d", i, id, originalIDs[i])
		}
	}
}

func TestDecodeMultipleHashIDs(t *testing.T) {
	hash1, err := EncodeIDs([]int{1})
	if err != nil {
		t.Fatal(err)
	}
	hash2, err := EncodeIDs([]int{2})
	if err != nil {
		t.Fatal(err)
	}
	decoded, err := DecodeHashIDs([]string{hash1, hash2})
	if err != nil {
		t.Fatal(err)
	}
	expected := []int{1, 2}
	if len(decoded) != len(expected) {
		t.Fatalf("Expected %d IDs, got %d", len(expected), len(decoded))
	}
	for i, id := range decoded {
		if id != expected[i] {
			t.Errorf("Mismatch at index %d: got %d, want %d", i, id, expected[i])
		}
	}
}

func TestDecodeInvalidHashID(t *testing.T) {
	_, err := DecodeHashIDs([]string{"invalid_hash"})
	if err == nil {
		t.Error("Expected error for invalid hash, got nil")
	}
}

func TestEncodeEmptyIDs(t *testing.T) {
	_, err := EncodeIDs([]int{})
	if err == nil {
		t.Error("Expected error when encoding empty IDs, got nil")
	}
}

func TestZeroID(t *testing.T) {
	originalIDs := []int{0}
	hashid, err := EncodeIDs(originalIDs)
	if err != nil {
		t.Fatal("EncodeIDs should handle zero, got error:", err)
	}
	decodedIDs, err := DecodeHashIDs([]string{hashid})
	if err != nil {
		t.Fatal("DecodeHashIDs failed:", err)
	}
	if len(decodedIDs) != 1 || decodedIDs[0] != originalIDs[0] {
		t.Errorf("Zero ID mismatch: got %d, want %d", decodedIDs[0], originalIDs[0])
	}
}
