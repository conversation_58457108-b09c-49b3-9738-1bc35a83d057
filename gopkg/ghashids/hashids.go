package ghashids

import (
	"errors"
	"fmt"
	"sync"

	"github.com/speps/go-hashids/v2"
)

var (
	hashIDInstance *hashids.HashID
	hashIDOnce     sync.Once
	hashIDErr      error
	hashSalt       string = "9+P)br8k*i0Glyne#M-ur&ZhsYUNyR0y"
	hashMinLength  int    = 10
)

// getHashID 获取全局唯一的hashids实例
func getHashID() (*hashids.HashID, error) {
	hashIDOnce.Do(func() {
		hd := hashids.NewData()
		hd.Salt = hashSalt
		hd.MinLength = hashMinLength
		hashIDInstance, hashIDErr = hashids.NewWithData(hd)
	})
	return hashIDInstance, hashIDErr
}

// DecodeHashIDs 解析多个hashid字符串并返回合并的ID集合
func DecodeHashIDs(hashIDs []string) ([]int, error) {
	h, err := getHashID()
	if err != nil {
		return nil, fmt.Errorf("hashids初始化失败: %w", err)
	}

	var result []int
	for _, hashStr := range hashIDs {
		decoded, err := h.DecodeWithError(hashStr)
		if err != nil {
			return nil, fmt.Errorf("hash解析失败[%s]: %w", hashStr, err)
		}
		result = append(result, decoded...)
	}
	return result, nil
}

// EncodeIDs 将整数集合编码为hashid字符串
func EncodeIDs(ids []int) (string, error) {
	if len(ids) == 0 {
		return "", errors.New("需要至少一个ID进行编码")
	}

	h, err := getHashID()
	if err != nil {
		return "", fmt.Errorf("hashids初始化失败: %w", err)
	}

	hashStr, err := h.Encode(ids)
	if err != nil {
		return "", fmt.Errorf("编码失败: %w", err)
	}

	if len(hashStr) < 10 {
		return "", fmt.Errorf("生成hash长度不足: %s", hashStr)
	}

	return hashStr, nil
}
