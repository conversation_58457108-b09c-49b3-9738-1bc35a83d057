package algo

import (
	"encoding/json"
	"fmt"
	"log"
	"os"
	"path"
	"sync"
	"time"
)

var SaveDir = path.Join(os.TempDir(), "abtest-csr-state")

func init() {
	log.Println("SaveDir", SaveDir)
	if err := os.MkdirAll(SaveDir, 0755); err != nil {
		log.Printf("create state dir %s failed: %v", SaveDir, err)
	}
}

type DistributeAlgo interface {
	GetCreateTime() time.Time
	GetLastDistributeTime() time.Time
	ResetSchemes(schemes []Scheme)
	Next() Scheme
	Close()
	ChangeStrict(strict bool)
}

type Algo struct {
	id                 string
	createTime         time.Time
	lastDistributeTime time.Time
	schemes            []Scheme
	schemeCount        int
	strict             bool // 是否进行严格检查，严格检查，在方案数量相同的也进行检查
	mux                sync.Mutex
}

func NewAlgo(id string, schemes []Scheme) *Algo {
	return &Algo{
		id:                 id,
		createTime:         time.Now(),
		lastDistributeTime: time.Now(),
		schemes:            schemes,
		schemeCount:        len(schemes),
		strict:             true,
	}
}

// ChangeStrict 更改是否严格检查
func (a *Algo) ChangeStrict(strict bool) {
	a.strict = strict
}

func (a *Algo) GetCreateTime() time.Time {
	//TODO implement me
	return a.createTime
}

func (a *Algo) GetLastDistributeTime() time.Time {
	//TODO implement me
	return a.lastDistributeTime
}

//	func (a *Algo) ResetScheme(schemes []Scheme) {
//		//TODO implement overwrite me
//		return
//	}
//

// CheckReset 检查是否要重置schemes列表
func (a *Algo) CheckReset(schemes []Scheme, checkWeight bool) bool {
	reset := false
	if len(schemes) != a.schemeCount {
		reset = true
	} else {
		if a.strict {
			hashMap := make(map[string]int)
			for _, scheme := range schemes {
				hashMap[scheme.Name] = scheme.Weight
			}
			for _, scheme := range a.schemes {
				if v, ok := hashMap[scheme.Name]; ok {
					if checkWeight && v != scheme.Weight {
						reset = true
						break
					}
				} else {
					reset = true
					break
				}
			}
		}
	}
	return reset
}

//
//func (a *Algo) Next() Scheme {
//	//TODO implement me
//
//	return Scheme{}
//}

//func (a *Algo) Close() {
//	//TODO implement me
//	panic("implement me")
//}

// saveAlgoStatus 保存算法状态
func saveAlgoStatus(id string, algoStatus map[string]int) error {
	if _, err := os.Stat(SaveDir); err != nil {
		err = os.MkdirAll(SaveDir, 0755)
		if err != nil {
			//sugared.Errorf("error creating algo save dir %s: %v", SaveDir, err)
			return err
		}
	}
	filename := fmt.Sprintf("%s/%s.json", SaveDir, id)
	file, err := os.OpenFile(filename, os.O_CREATE|os.O_WRONLY, 0755)
	if err != nil {
		//sugared.Errorf("error opening algo save file %s: %v", filename, err)
		return err
	}
	defer file.Close()
	encoder := json.NewEncoder(file)
	err = encoder.Encode(algoStatus)
	if err != nil {
		//sugared.Errorf("error encoding algo save file %s: %v", filename, err)
		return err
	}
	return nil
}

// loadAlgoStatus 从文件载入算法状态
func loadAlgoStatus(id string) (algoStatus map[string]int) {
	algoStatus = make(map[string]int)
	filename := fmt.Sprintf("%s/%s.json", SaveDir, id)
	file, err := os.Open(filename)
	if err != nil {
		//sugared.Errorf("error opening algo save file %s: %v", filename, err)
		return nil
	}
	defer file.Close()
	decoder := json.NewDecoder(file)
	err = decoder.Decode(&algoStatus)
	if err != nil {
		//sugared.Errorf("error decoding algo save file %s: %v", filename, err)
		return nil
	}
	_ = os.Remove(filename)
	return algoStatus
}
