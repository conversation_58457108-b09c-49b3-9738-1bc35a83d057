package algo

import (
	"log/slog"
	"sync"

	"github.com/cockroachdb/errors"
	"github.com/samber/lo"
)

const (
	TypeRoundRobin         = "round-robin"
	TypeSodokuRoundRobin   = "sodoku-round-robin"
	TypeWeightedRoundRobin = "weighted-round-robin"
)

type Factory struct {
	algos map[string]map[string]DistributeAlgo
	mux   sync.Mutex
}

var factory *Factory

func init() {
	factory = &Factory{
		algos: make(map[string]map[string]DistributeAlgo),
	}
}

// NewFactory 新建算法工厂
func NewFactory() *Factory {
	return factory
}

// NewAlgo 新建算法
func (f *Factory) NewAlgo(algoType string, id string, schemes []Scheme) (algo DistributeAlgo, err error) {
	f.mux.Lock()
	defer f.mux.Unlock()
	//var algo DistributeAlgo
	var ok bool
	if _, ok = f.algos[algoType]; !ok {
		f.algos[algoType] = make(map[string]DistributeAlgo)
	}
	if algo, ok = f.algos[algoType][id]; !ok {
		switch algoType {
		case TypeRoundRobin:
			algo, err = NewRoundRobin(id, schemes)
		case TypeSodokuRoundRobin:
			schemes = lo.Shuffle(schemes)
			algo, err = NewSudokuRoundRobin(id, schemes)
			slog.Info("NewSudokuRoundRobin", "id", id, "schemes", schemes)
		case TypeWeightedRoundRobin:
			algo, err = NewWeightRoundRobin(id, schemes)
		default:
			return nil, errors.New("unknown algotype: " + algoType)
		}
		if err == nil {
			f.algos[algoType][id] = algo
		} else {
			return nil, err
		}
	} else {
		algo = f.algos[algoType][id]
	}
	algo.ChangeStrict(false)
	return algo, nil
}
