package service

import (
	"context"
	"fmt"
	"log"
	"sync"
	"testing"

	adminmodel "git.7k7k.com/data/abAdmin/model"
	"git.7k7k.com/data/abScheduler/model"
	"git.7k7k.com/data/abScheduler/repository"
	"git.7k7k.com/data/abScheduler/repository/dao"
	"git.7k7k.com/pkg/storage"
	"github.com/glebarez/sqlite"
	"github.com/samber/lo"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
)

func init() {
	log.SetFlags(log.LstdFlags | log.Lshortfile)
}

// 测试用模型
type TestMetaModel struct {
	ID   int64 `gorm:"primarykey"`
	Name string
}

func (t TestMetaModel) GetID64() int64 {
	return t.ID
}

// 简单的内存 KV 存储实现
type memoryKVStore[T any] struct {
	lock sync.RWMutex
	data map[int64]*T

	storage.CacheStub[int64, *T]
}

func newMemoryKVStore[T any]() *memoryKVStore[T] {
	return &memoryKVStore[T]{
		data: make(map[int64]*T),
	}
}

func (m *memoryKVStore[T]) MSet(ctx context.Context, values map[int64]*T) error {
	for k, v := range values {
		m.lock.Lock()
		m.data[k] = v
		m.lock.Unlock()
	}
	return nil
}

func TestSyncMeta(t *testing.T) {
	// 设置 SQLite 内存数据库
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	assert.NoError(t, err)

	// 创建测试表
	err = db.AutoMigrate(&TestMetaModel{})
	assert.NoError(t, err)

	// 插入测试数据

	testData := []TestMetaModel{}
	c1, c2 := 123, 345
	for i := 1; i <= c1; i++ {
		testData = append(testData, TestMetaModel{ID: int64(i), Name: fmt.Sprintf("Test%d", i)})
	}

	err = db.Create(&testData).Error
	assert.NoError(t, err)
	// t.Log("db insert count", len(testData))

	// 创建内存 KV 存储
	store := newMemoryKVStore[TestMetaModel]()

	// 执行同步
	filterNothing := func(query *gorm.DB) *gorm.DB { return query }
	_, err = loadMeta(context.Background(), db, filterNothing, store, true)
	assert.NoError(t, err)
	// t.Log("store count", len(store.data))

	// 验证同步结果
	assert.Equal(t, len(testData), len(store.data))
	for _, td := range testData {
		syncedData, exists := store.data[td.ID]
		assert.True(t, exists)
		assert.Equal(t, td.Name, syncedData.Name)
	}

	// 测试批次处理
	t.Run("batch processing", func(t *testing.T) {
		// 清空存储
		store = newMemoryKVStore[TestMetaModel]()

		// 插入更多数据以测试批处理
		var moreDatas []TestMetaModel
		for i := c1 + 1; i <= c2; i++ {
			moreDatas = append(moreDatas, TestMetaModel{
				ID:   int64(i),
				Name: fmt.Sprintf("Test%d", i),
			})
		}
		err = db.Create(&moreDatas).Error
		assert.NoError(t, err)

		// 再次执行同步
		_, err = loadMeta(context.Background(), db, filterNothing, store, true)
		assert.NoError(t, err)

		// 验证所有数据都被同步
		assert.Len(t, store.data, c2)
	})
}

var setupDBOnce sync.Once
var testingDB *gorm.DB

func setupTestDB() *gorm.DB {
	setupDBOnce.Do(func() {
		var err error
		testingDB, err = gorm.Open(sqlite.Open("file::memory:?cache=shared"), &gorm.Config{})
		MustNil(err)

		sqldb, err := testingDB.DB()
		MustNil(err)
		sqldb.SetMaxOpenConns(1)

		// 创建测试表
		err = testingDB.AutoMigrate(
			&adminmodel.Project{},
			&adminmodel.Layer{},
			&adminmodel.Exp{},
			&adminmodel.Group{},
			&adminmodel.SplitGroup{},
			&model.UserState{},
		)
		MustNil(err)

		// 插入 mock 数据
		mock := createMockData()
		MustNil(testingDB.Create(&mock.projects).Error)
		MustNil(testingDB.Create(&mock.layers).Error)
		MustNil(testingDB.Debug().Create(&mock.exps).Error)
		MustNil(testingDB.Create(&mock.groups).Error)

		// TODO 上面的 Create 会通过 BeforeCreate 把 state 改为 1，这里临时打个补丁
		expIds := lo.Map(mock.exps, func(exp *adminmodel.Exp, _ int) int64 { return exp.ID })
		MustNil(testingDB.Table("exp").Where("id in ?", expIds).Update("state", adminmodel.ExpStatusRunning).Error)
	})

	return testingDB
}

func setupDataTransfer() *DataTransfer {
	db := setupTestDB()
	return &DataTransfer{
		DB: (*repository.QueryAdmin)(dao.Use(db.Debug())),
		Store: &ConfigMetaStore{
			Project:    newMemoryKVStore[adminmodel.Project](),
			Layer:      newMemoryKVStore[adminmodel.Layer](),
			Exp:        newMemoryKVStore[adminmodel.Exp](),
			Group:      newMemoryKVStore[adminmodel.Group](),
			SplitGroup: newMemoryKVStore[adminmodel.SplitGroup](),
		},
	}
}

func TestDataTransfer_syncMetaFromDB(t *testing.T) {
	mock := createMockData()
	dt := setupDataTransfer()

	result, err := dt.loadMetaFromDB(context.Background(), true, nil)
	assert.NoError(t, err)

	// 验证同步结果
	assert.Equal(t, len(mock.projects), len(result.Projects))
	assert.Equal(t, len(mock.layers), len(result.Layers))
	assert.Equal(t, len(mock.groups), len(result.Groups))
	assert.Equal(t, len(mock.exps), len(result.Exps))

	// 只验证有效状态的实验数量
	validExps := lo.Filter(mock.exps, func(exp *adminmodel.Exp, _ int) bool {
		return lo.Contains(ValidExpStates, adminmodel.ExpState(exp.State))
	})
	assert.Equal(t, len(validExps), len(result.Exps))
}

func TestDataTransfer_buildMetaToIndex(t *testing.T) {
	mock := createMockData()
	dt := &DataTransfer{}

	// 使用 ValidExpStates 过滤有效状态的实验
	validExps := lo.Filter(mock.exps, func(exp *adminmodel.Exp, _ int) bool {
		return lo.Contains(ValidExpStates, adminmodel.ExpState(exp.State))
	})

	memStore := &MemStore{
		Projects: mock.projects,
		Layers:   mock.layers,
		Exps:     validExps, // 使用过滤后的实验列表
		Groups:   mock.groups,
	}

	index, err := dt.buildMetaToIndex(context.Background(), memStore)
	assert.NoError(t, err)

	// 验证索引构建结果
	t.Run("verify projects", func(t *testing.T) {
		assert.Len(t, index.ProjectsByID, len(mock.projects))
		assert.Len(t, index.ProjectsByKey, len(mock.projects))

		prj := index.ProjectsByID[1]
		assert.Equal(t, "project1", prj.Key)
		assert.Equal(t, 1, prj.LayerExclusiveID)
		assert.Len(t, prj.Layers, 2)
	})

	t.Run("verify layers", func(t *testing.T) {
		assert.Len(t, index.PrjLayers[1], 2)

		layer1 := index.PrjLayers[1][1]
		assert.True(t, layer1.Exclusive)
		assert.Equal(t, "p1_layer1", layer1.Name)
		assert.ElementsMatch(t, []int{1, 2}, layer1.ExpIDs)

		layer2 := index.PrjLayers[1][2]
		assert.False(t, layer2.Exclusive)
		assert.Equal(t, "p1_layer2", layer2.Name)
		assert.ElementsMatch(t, []int{3}, layer2.ExpIDs)
	})

	t.Run("verify exps", func(t *testing.T) {
		assert.Len(t, index.Exps, 3)

		exp1 := index.Exps[1]
		assert.Equal(t, int8(adminmodel.ExpStatusRunning), exp1.Status)
		assert.Equal(t, int16(50), exp1.Prop)
		assert.ElementsMatch(t, []int{1, 2, 3}, exp1.RunningGroupIds)

		exp2 := index.Exps[2]
		assert.Equal(t, int16(30), exp2.Prop)
		assert.ElementsMatch(t, []int{4, 5}, exp2.RunningGroupIds)
	})

	t.Run("verify groups", func(t *testing.T) {
		assert.Len(t, index.Groups, 7)

		group := index.Groups[1]
		assert.Equal(t, "p1_layer1_exp1_g1", group.Key)
		assert.JSONEq(t, `{"key":"value1"}`, group.ParamJSON)
	})

	t.Run("verify white list", func(t *testing.T) {
		layer := index.PrjLayers[1][1]
		assert.Len(t, layer.UserWhiteList, 10)
		assert.Equal(t, 1, layer.UserWhiteList["user1"])
		assert.Equal(t, 1, layer.UserWhiteList["user2"])
	})

	t.Run("verify domains", func(t *testing.T) {
		prj := index.ProjectsByID[1]
		assert.NotNil(t, prj.Domains)
		assert.Len(t, prj.Domains, 2)

		// 验证独占域
		exclusiveDomain := prj.Domains[0]
		assert.Contains(t, exclusiveDomain.LayerIDs, 1)
		assert.Equal(t, int32(500), exclusiveDomain.ModRight)

		// 验证非独占域
		nonExclusiveDomain := prj.Domains[1]
		assert.Contains(t, nonExclusiveDomain.LayerIDs, 2)
		assert.Equal(t, int32(model.DomainMod), nonExclusiveDomain.ModRight)
	})
}

func TestCompactJSON(t *testing.T) {
	tests := []struct {
		name    string
		input   string
		want    string
		wantErr bool
	}{
		{
			name:    "正常JSON",
			input:   `{"name": "test", "age": 18}`,
			want:    `{"name":"test","age":18}`,
			wantErr: false,
		},
		{
			name: "带空格和换行的JSON",
			input: `{
				"name": "test",
				"age": 18
			}`,
			want:    `{"name":"test","age":18}`,
			wantErr: false,
		},
		{
			name:    "无效的JSON",
			input:   `{"name": "test", }`,
			want:    "",
			wantErr: true,
		},
		{
			name:    "空字符串",
			input:   "",
			want:    "",
			wantErr: true,
		},
		{
			name:    "嵌套JSON",
			input:   `{"user": {"name": "te st", "age": 18}, "active": true}`,
			want:    `{"user":{"name":"te st","age":18},"active":true}`,
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := CompactJSON(tt.input)
			if (err != nil) != tt.wantErr {
				t.Errorf("CompactJSON() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("CompactJSON() = %v, want %v", got, tt.want)
			}
		})
	}
}
