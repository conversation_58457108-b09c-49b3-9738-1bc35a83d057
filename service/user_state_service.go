package service

import (
	"context"
	"fmt"
	"log/slog"
	"maps"
	"os"
	"runtime"
	"strconv"
	"time"

	admodel "git.7k7k.com/data/abAdmin/model"
	"git.7k7k.com/data/abScheduler/infra/config"
	"git.7k7k.com/data/abScheduler/infra/metric"
	"git.7k7k.com/data/abScheduler/infra/redises"
	"git.7k7k.com/data/abScheduler/model"
	"git.7k7k.com/data/abScheduler/repository/cache"
	"git.7k7k.com/data/abScheduler/repository/mongos"
	"git.7k7k.com/data/abScheduler/repository/mysql"
	"git.7k7k.com/pkg/common/ctxs"
	"git.7k7k.com/pkg/common/gg"
	"git.7k7k.com/pkg/common/gosentry"
	"git.7k7k.com/pkg/common/queue"
	"github.com/cockroachdb/errors"
	"github.com/samber/lo"
	"github.com/spf13/cast"
	"go.mongodb.org/mongo-driver/bson"
)

type UserStateService struct {
	cache     *cache.UserStateCache // 用户状态缓存服务
	mongoRepo *mongos.UserStateRepo // 用户状态存储仓库，替代之前直接操作MongoDB的逻辑
	mysqlRepo *mysql.UserStateRepo  // 用户状态存储仓库，替代之前直接操作MySQL的逻辑

	configService *ConfigService
	notifyService *NotifyService

	upsertQueue   *queue.KafkaQueue[model.UserState]
	deleteQueue   *queue.ChanQueue[model.UserState]
	metricService *MetricService

	redisClts *redises.ClientMgr
}

const RedisPrefixUserState = "uste"

// @autowire(set=service)
func NewUserStateService(
	cfg *config.Config,
	clts *redises.ClientMgr, mongoSet *mongos.MongoSet, mysqlRepo *mysql.UserStateRepo,
	configService *ConfigService, notifyService *NotifyService, metricService *MetricService,
) (*UserStateService, func()) {

	userStoreRepo := mongos.NewUserStateRepo(mongoSet)

	s := &UserStateService{
		cache:         cache.NewUserStateCache(clts.UserStateRedis, clts.UserStateTierRedis),
		configService: configService,
		notifyService: notifyService,
		metricService: metricService,
		redisClts:     clts,
		mongoRepo:     userStoreRepo,
		mysqlRepo:     mysqlRepo,
	}

	topic := "abtest_user_state_upsert"
	consumerGroup := "abtest_user_state_upsert_consumer"
	if runtime.GOOS == "darwin" {
		topic += "_local"
		consumerGroup += "_local"
	}

	s.upsertQueue = queue.NewKafkaQueue[model.UserState](queue.KafkaConfig{
		Brokers:       cfg.MQ.Kafka["abtest_state"].Addrs,
		Topic:         topic,
		GroupID:       consumerGroup,
		Async:         true,
		SSL:           cfg.MQ.Kafka["abtest_state"].SSL,
		QueueCapacity: 500,
	})
	s.deleteQueue = queue.NewChanQueue[model.UserState](10000)

	cleanup := func() {}

	if os.Getenv("DISABLE_MQ") != "1" {
		ctx, cancel := context.WithCancel(context.Background())
		co := ctxs.NewCoordinator(ctx)
		go s.flushToDB(ctx, co)

		cleanup = func() {
			cancel()
			slog.Info("flushToDB cleanup...")
			t1 := time.Now()
			co.Wait()
			slog.Info("flushToDB cleanup done", "time_cost", time.Since(t1).String())
		}
	}

	return s, cleanup
}

// 使用 cache 中定义的类型
type (
	PrjCache       = cache.PrjCache
	LayersHitState = cache.PrjCache
	HitState       = cache.HitState
)

// buildCacheKey 构建用户状态的 Redis key
// 为了兼容性保留此方法，但内部调用 cache.BuildCacheKey
func (s *UserStateService) buildCacheKey(prjID int, userID string) string {
	return s.cache.BuildCacheKey(prjID, userID)
}

// GetOneCache 获取单个层缓存
func (s *UserStateService) GetOneCache(ctx context.Context, prjID int, userID string, layerID int) (hitState *HitState, err error) {
	return s.cache.GetOne(ctx, prjID, userID, layerID)
}

// Gets 获取用户状态，一个项目下的所有层缓存
func (s *UserStateService) Gets(ctx context.Context, prjID int, userID string) (states PrjCache, err error) {
	states, err = s.cache.Gets(ctx, prjID, userID)
	if err != nil {
		return
	}

	if len(states) > 0 {
		return
	}

	prj, _ := s.configService.ProjectByID.Get(ctx, prjID)

	coll := s.mongoRepo.Coll()
	stateModels := []model.UserState{}
	err = coll.Find(ctx, bson.M{"uid": userID, "utype": model.UserTypeVisitor, "layer_id": bson.M{"$in": prj.Layers}}).All(&stateModels)
	if err != nil {
		return
	}

	for _, state := range stateModels {
		states[state.LayerId] = &cache.HitState{
			ExpID:     state.ExpId,
			GroupID:   state.GrpId,
			EnterAt:   int(state.EnterAt.Unix()),
			ExpiredAt: int(state.ExpireAt.Unix()),
		}
	}

	return
}

// Update 根据实验分配结果更新 redis 和 mongo 的用户状态
func (s *UserStateService) Update(ctx context.Context, prjID int, userID string, layersState PrjCache, befores PrjCache) (err error) {
	_, finish := gosentry.StartSpan(ctx, "UpdateState", "", "")
	defer finish()

	maps.DeleteFunc(layersState, func(k int, gs *HitState) bool {
		return gs == nil || gs.GroupID == 0
	})
	beforeLayerIds := lo.Keys(befores)
	afterLayerIds := lo.Keys(layersState)
	if ctxs.IsDebug(ctx) {
		slog.InfoContext(ctx, "UpdateState", "before", beforeLayerIds, "after", afterLayerIds)
	}

	// 更新 Redis
	if len(layersState) > 0 {
		if err = s.cache.SetLayers(ctx, prjID, userID, layersState); err != nil {
			return err
		}
	}

	// 更新 Redis：删除层
	removedLayerIds := lo.FilterMap(beforeLayerIds, func(layerID int, _ int) (int, bool) {
		_, exists := layersState[layerID]
		return layerID, !exists
	})
	if len(removedLayerIds) > 0 {
		_ = s.RemoveCache(ctx, prjID, userID, removedLayerIds...)
	}

	// TODO 对于长期不活跃的用户，需要有清理机制

	// mongo: upsert
	upserts := s.buildNewOrUpdatedStates(ctx, userID, layersState, befores)
	if len(upserts) > 0 {
		metric.MQWriteCount.WithLabelValues("abtest_user_state_upsert").Add(float64(len(upserts)))
		err := s.upsertQueue.PushBatch(ctx, upserts)
		if err != nil {
			slog.ErrorContext(ctx, "PushBatch", "err", err.Error())
		}
		upsertIds := lo.Map(upserts, func(us model.UserState, _ int) int { return us.GrpId })
		slog.InfoContext(ctx, "buildNewOrUpdatedStates", "upsertIds", upsertIds)
	}

	// mongo: delete
	deletes := s.buildDeletedStates(ctx, userID, layersState, befores)
	if len(deletes) > 0 {
		s.deleteQueue.PushBatch(ctx, deletes)
		deleteIds := lo.Map(deletes, func(us model.UserState, _ int) int { return us.LayerId })
		slog.InfoContext(ctx, "buildDeletedStates", "deleteLayerIds", deleteIds)
	}

	return nil
}

// RemoveCache 按层删除用户状态
func (s *UserStateService) RemoveCache(ctx context.Context, prjID int, userID string, layerIDs ...int) (err error) {
	return s.cache.RemoveLayers(ctx, prjID, userID, layerIDs...)
}

// ListWithExp 拿到实验里所有uid
func (s *UserStateService) ListWithExp(ctx context.Context, prjID, expID int) (uidsCh chan []string, err error) {
	return s.mongoRepo.ListWithExp(ctx, expID, 500)
}

func (s *UserStateService) buildNewOrUpdatedStates(ctx context.Context, userID string, layersState, befores PrjCache) []model.UserState {
	return lo.FilterMap(lo.Entries(layersState), func(entry lo.Entry[int, *HitState], _ int) (model.UserState, bool) {
		layerID, after := entry.Key, entry.Value
		before, existed := befores[layerID]

		if !existed || before.GroupID != after.GroupID { // 新增层或更新层
			grp, err := s.configService.GroupIdx.Get(ctx, after.GroupID)
			if err != nil {
				slog.ErrorContext(ctx, "get_group_failed", "group_id", after.GroupID, "error", err.Error())
				return model.UserState{}, false
			}

			return s.mongoRepo.CreateUserState(
				userID,
				layerID,
				after.GroupID,
				grp.ExpID,
				time.Unix(int64(after.ExpiredAt), 0),
			), true
		}
		return model.UserState{}, false
	})
}

func (s *UserStateService) buildDeletedStates(_ context.Context, userID string, layersState, befores PrjCache) []model.UserState {
	return lo.FilterMap(lo.Entries(befores), func(entry lo.Entry[int, *HitState], _ int) (model.UserState, bool) {
		layerID := entry.Key
		if _, exists := layersState[layerID]; !exists {
			return model.UserState{
				UType:    model.UserTypeVisitor,
				UID:      userID,
				LayerId:  layerID,
				ExpireAt: time.Now(), // 过期时间设置为当前时间
			}, true
		}
		return model.UserState{}, false
	})
}

func (s *UserStateService) flushToDB(process context.Context, co *ctxs.Coordinator) {
	// 添加定时更新队列大小的 goroutine
	co.Go(func() {
		ticker := time.NewTicker(time.Second)
		defer ticker.Stop()

		for {
			select {
			case <-co.ParentDone():
				s.upsertQueue.WaitAndClose(process)
				s.deleteQueue.WaitAndClose(process)
				return
			case <-ticker.C:
				// metric.QueueSize.WithLabelValues("user_state_upsert").Set(float64(s.upsertQueue.Size()))
				metric.QueueSize.WithLabelValues("user_state_delete").Set(float64(s.deleteQueue.Size()))
			}
		}
	})

	co.Go(func() {
		s.upsertQueue.ListenBatch(process, queue.ConsumConfig[model.UserState]{
			WorkerSize:    8,
			FlushInterval: time.Second / 10,
			FlushSize:     200,
			HandleFunc: func(states []model.UserState) error {
				ctx := context.Background()

				metric.MQReadCount.WithLabelValues("abtest_user_state_upsert", "abtest_user_state_upsert_consumer").Add(float64(len(states)))

				go s.metricService.SetRedisStats(ctx, states) // 这里会写到国内，所以需要异步

				// user_state 用户状态 MySQL - 使用 mysqlRepo
				err := s.mysqlRepo.UpsertUserStates(ctx, states)
				if err != nil {
					slog.ErrorContext(ctx, "flush_to_mysql_failed", "error", err.Error())
					return err
				}

				// user_state 用户状态 MongoDB - 使用 userStoreRepo
				err = s.mongoRepo.UpsertUserStates(ctx, states)
				if err != nil {
					return err
				}

				return nil
			},
		})
	})

	go s.deleteQueue.ListenBatch(process, queue.ConsumConfig[model.UserState]{
		WorkerSize:    5,
		FlushInterval: time.Second * 1,
		FlushSize:     100,
		HandleFunc: func(states []model.UserState) error {
			ctx := context.Background()

			// user_state 用户状态 MySQL - 使用 mysqlRepo
			err := s.mysqlRepo.DeleteUserStates(ctx, states)
			if err != nil {
				slog.ErrorContext(ctx, "flush_to_mysql_failed", "error", err.Error())
				return err
			}

			// user_state 用户状态 MongoDB - 使用 userStoreRepo
			return s.mongoRepo.DeleteUserStates(ctx, states)
		},
	})

	go func() {
		for range time.Tick(time.Second * 5) {
			slog.Info("upsertQueue", "stats", s.upsertQueue.Stats())
		}
	}()
}

type UserStateNotifyMsg struct{}

// moveUIDto 重定向用户
// 1. 逐条更新 DB
// 2. 重建为 uid 维度
// 3. 更新到 redis
// 4. 通知集群其他节点更新本地缓存
func (s *UserStateService) moveUIDto(ctx context.Context, prjId int, toLayerId int, uss []model.UserState, change *admodel.SplitGroup) error {
	// 1. 更新 MongoDB 数据库
	err := s.mongoRepo.MoveUserStates(ctx, uss, toLayerId, int(change.ToGroupID), int(change.ToExpID))
	if err != nil {
		return err
	}

	// 1.1 更新 MySQL 数据库 - 使用 mysqlRepo
	ids := lo.Map(uss, func(us model.UserState, _ int) int { return us.ID })
	err = s.mysqlRepo.MoveUserStates(ctx, ids, toLayerId, int(change.ToGroupID), int(change.ToExpID))
	if err != nil {
		return errors.Wrap(err, "update db failed")
	}

	for i := range uss {
		uss[i].LayerId = toLayerId
		uss[i].GrpId = int(change.ToGroupID)
		uss[i].ExpId = int(change.ToExpID)
	}

	// 2. 按 uid 重组数据
	userStates := make(map[string]PrjCache)
	for _, us := range uss {
		if _, ok := userStates[us.UID]; !ok {
			userStates[us.UID] = make(PrjCache)
		}
		userStates[us.UID][us.LayerId] = &HitState{
			ExpID:     int(us.ExpId),
			GroupID:   int(us.GrpId),
			EnterAt:   int(us.EnterAt.Unix()),
			ExpiredAt: int(us.ExpireAt.Unix()),
		}
	}

	// 3. 批量更新 Redis
	for uid, layerState := range userStates {
		if err := s.cache.SetLayers(ctx, prjId, uid, layerState); err != nil {
			return fmt.Errorf("update redis failed: %w", err)
		}
	}

	// 更新 toGrp 在 redis 里的人数
	slog.InfoContext(ctx, "update_redis", "size", len(uss), "to_grp_id", uss[0].GrpId, "to_exp_id", uss[0].ExpId)
	err = s.metricService.SetRedisStats(ctx, uss)
	if err != nil {
		return err
	}

	// 4. 通知集群其他节点
	// msg := UserStateNotifyMsg{}
	// if err := s.notifyService.UserStateNotifier.Send(ctx, msg); err != nil {
	// 	return fmt.Errorf("notify cluster failed: %w", err)
	// }

	return nil
}

// ReCountStatsToRedis 更新<实验>实时人数到 redis
func (s *UserStateService) ReCountStatsToRedis(ctx context.Context) (err error) {
	now := time.Now()
	defer func() {
		slog.InfoContext(ctx, "ReCountStatsToRedis defer", "time_cost", time.Since(now).String())
	}()

	expIds, err := s.mongoRepo.FetchAllExpIds(ctx)
	if err != nil {
		return errors.Wrap(err, "get_exp_ids_failed")
	}
	slog.InfoContext(ctx, "ReCountStatsToRedis", "expIds", expIds, "len", len(expIds))

	w := queue.NewChanQueue[int](100)
	go w.ListenBatch(ctx, queue.ConsumConfig[int]{
		WorkerSize:    1,
		FlushInterval: time.Second / 100,
		FlushSize:     5,
		HandleFunc: func(expIds []int) (err error) {
			// slog.InfoContext(ctx, "ReCountStatsToRedis HandleFunc", "expIds", expIds)

			exps, err := s.configService.GetExps(ctx, expIds)
			if err != nil {
				return
			}

			g2e := map[int]int{}
			grpIds := []int{}
			for _, exp := range exps {
				grpIds = append(grpIds, exp.AllGroupIds...)
				for _, grpId := range exp.AllGroupIds {
					g2e[grpId] = exp.ID
				}
			}

			expCnt := map[int]int64{}
			for _, grpId := range grpIds {
				c, err := s.CountGrpStateToRedis(ctx, cast.ToInt64(grpId))
				if err != nil {
					return err
				}
				expCnt[g2e[grpId]] += c
			}
			for _, exp := range exps {
				prj := gg.ResultOf(s.configService.GetProject(ctx, exp.PrjID, "")).Must()
				metric.AbStatsExpUserCountGauge.WithLabelValues(prj.MetricKey(), strconv.Itoa(exp.ID)).Set(float64(expCnt[exp.ID]))
			}
			slog.InfoContext(ctx, "CountGrpStateToRedis done", "expCnt", expCnt)
			return
		},
	})
	for _, expId := range expIds {
		w.Push(ctx, cast.ToInt(expId))
	}
	w.WaitAndClose(ctx)

	return
}

// CountGrpStateToRedis 更新<实验组>的实时人数到 redis
func (s *UserStateService) CountGrpStateToRedis(ctx context.Context, grpId int64) (count int64, err error) {
	// 1. 从MongoDB中获取实验组的用户数量，使用 userStoreRepo
	count, err = s.mongoRepo.CountGroupUserStatesV2(ctx, grpId)
	if err != nil {
		return 0, err
	}

	// 2. 将结果存入Redis
	grpCount := map[int64]int64{
		grpId: count,
	}

	err = s.metricService.RealtimeSetGroup(ctx, grpCount, 180*time.Minute)
	if err != nil {
		return 0, errors.Wrap(err, "set redis failed")
	}

	return count, nil
}

// CleanProject 清理项目下的所有缓存，仅用来清理压测数据
func (s *UserStateService) CleanProject(ctx context.Context, prjId int) {
	s.cache.CleanProject(ctx, prjId)
}

// CleanExpiredUserStates 清理 MySQL 中过期的用户状态
// 将过期用户的 exp_id、grp_id、expire_at 设置为 0
func (s *UserStateService) CleanExpiredUserStates(ctx context.Context) error {
	batchSize := 200
	totalAffected := int64(0)

	for {
		affected, err := s.mysqlRepo.CleanExpiredUserStates(ctx, batchSize)
		if err != nil {
			return errors.Wrap(err, "查询过期用户状态失败")
		}

		totalAffected += affected
		slog.InfoContext(ctx, "clean_expired_user_states", "batch_size", batchSize, "affected_rows", affected)

		if affected < int64(batchSize) {
			break
		}

		// 避免过快占用数据库资源
		time.Sleep(time.Millisecond * time.Duration(batchSize) / 100)
	}

	slog.InfoContext(ctx, "clean_expired_user_states done", "total_affected", totalAffected)
	return nil
}
