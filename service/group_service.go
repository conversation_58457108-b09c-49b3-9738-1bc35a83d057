package service

import (
	"context"

	"git.7k7k.com/data/abScheduler/infra/redises"
)

type GroupService struct {
	ConfigService *ConfigService
}

type GroupRequest struct {
	ProjectKey string `json:"project_key"`
	GroupIds   []int  `json:"group_ids"`
}
type Group struct {
	Id        int    `json:"id"`
	Status    int8   `json:"status"`
	ParamJson string `json:"param_json"`
	ExpId     int    `json:"exp_id"`
	GroupName string `json:"group_name"`
}
type GroupResponse struct {
	Groups map[int]*Group `json:"groups"`
}

// @autowire(set=service)
func NewGroupService(clts *redises.ClientMgr, configService *ConfigService) *GroupService {
	s := &GroupService{
		ConfigService: configService,
	}
	return s
}

func (s *GroupService) GetGroupInfo(ctx context.Context, request GroupRequest) (response *GroupResponse, err error) {
	response = &GroupResponse{
		Groups: make(map[int]*Group),
	}

	// 获取项目
	prj, err := s.ConfigService.GetProject(ctx, 0, request.ProjectKey)
	if err != nil {
		return nil, err
	}
	// 获取方案
	groups, err := s.ConfigService.GetGroup(ctx, request.GroupIds)
	if err != nil {
		return nil, err
	}
	for _, group := range groups {
		// 过滤掉不在项目中的方案
		if group.PrjID != prj.ID {
			continue
		}
		response.Groups[group.ID] = &Group{
			Id:        group.ID,
			Status:    group.Status,
			ParamJson: group.ParamJSON,
			ExpId:     group.ExpID,
			GroupName: group.GroupName,
		}
	}
	return response, nil
}
