package service

import (
	"context"
	"time"

	"git.7k7k.com/data/abScheduler/model"
)

// @autowire(set=service)
type UserLeaveService struct {
	ConfigService    *ConfigService
	UserStateService *UserStateService
}

type LeaveRequest struct {
	ProjectKey string `json:"project_key"`
	UID        string `json:"uid"`
}

type LeaveResponse struct {
	DeleteCount int `json:"delete_count"`
}

func (s *UserLeaveService) Leave(ctx context.Context, request *LeaveRequest) (response *LeaveResponse, err error) {
	response = &LeaveResponse{}

	project, err := s.ConfigService.GetProject(ctx, 0, request.ProjectKey)
	if err != nil {
		return
	}

	count, err := s.UserStateService.cache.RemoveAll(ctx, project.ID, request.UID)
	if err != nil {
		return
	}

	layerIds := project.Layers
	states := make([]model.UserState, len(layerIds))
	for i, layerId := range layerIds {
		states[i] = model.UserState{
			UID:      request.UID,
			UType:    model.UserTypeVisitor,
			LayerId:  layerId,
			ExpireAt: time.Now(), // 过期时间设置为当前时间
		}
	}

	err = s.UserStateService.mongoRepo.DeleteUserStates(ctx, states)
	if err != nil {
		return
	}

	err = s.UserStateService.mysqlRepo.DeleteUserStates(ctx, states)
	if err != nil {
		return
	}

	response.DeleteCount = int(count)

	return
}
