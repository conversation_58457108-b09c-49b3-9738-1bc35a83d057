package httpd

import (
	"context"
	"crypto/sha1"
	"errors"
	"fmt"
	"log"
	"net/http"
	"strconv"
	"time"

	"git.7k7k.com/data/abScheduler/httpd/base"
	middlewarePrivate "git.7k7k.com/data/abScheduler/httpd/middleware"
	"git.7k7k.com/data/abScheduler/infra/config"
	"git.7k7k.com/data/abScheduler/infra/redises"
	"git.7k7k.com/data/abScheduler/service"
	"git.7k7k.com/data/abScheduler/service/abtest"
	"git.7k7k.com/pkg/common/http/middleware"
	"github.com/bytedance/sonic"
	"github.com/gin-contrib/cors"
	"github.com/gin-contrib/pprof"
	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator"

	sentrygin "github.com/getsentry/sentry-go/gin"
	"github.com/prometheus/client_golang/prometheus/promhttp"
)

// @autowire(set=http)
type Handlers struct {
	RedisMgr *redises.ClientMgr

	AbTestHandler

	AbtestService *abtest.DistributeService

	DataTransfer     *service.DataTransfer
	UserStateService *service.UserStateService
	ConfigService    *service.ConfigService
	RedirectService  *service.RedirectService
	WhiteCombService *service.WhiteCombService
	GroupService     *service.GroupService
	CleanService     *service.CleanService
	UserLeaveService *service.UserLeaveService
	DebugService     *service.DebugService
	HashService      *service.HashService
}

// @autowire(set=http)
func NewHTTPServer(config *config.Config, handlers *Handlers) (*http.Server, func()) {
	g := gin.New()
	g.Use(
		middleware.LogAndMetricWith(!config.IsProd()),
		middleware.OpenTracing(),
		middlewarePrivate.Recover(),
		sentrygin.New(sentrygin.Options{Repanic: true}),
		middleware.Sentry(),
		cors.Default(),
	)

	g.GET("/ping", func(c *gin.Context) { c.JSON(http.StatusOK, gin.H{"message": "pong"}) })
	g.GET("/health", func(c *gin.Context) { c.JSON(http.StatusOK, gin.H{"status": "OK"}) })
	g.GET("/metrics", MetricBefore, gin.WrapH(promhttp.Handler()))
	g.GET("/debug/sleep", SleepAPI)
	pprof.Register(g, "/debug/pprof")

	//
	// 业务接口
	// {"code": 200, "message": "", "data": {}}
	//

	Route(g, "GET", "/debug/project-state", handlers.DebugService.ProjectState)
	Route(g, "GET", "/config/sync", handlers.DataTransfer.RefreshAPI)

	api := g.Group("abtest")
	handlers.AbtestService.Init(config)
	Route(api, "POST", "/traffic", handlers.AbtestService.Traffic)
	Route(api, "POST", "/leave", handlers.UserLeaveService.Leave)
	Route(api, "POST", "/setwhitecomb", handlers.WhiteCombService.SetWhiteComb)
	Route(api, "POST", "/config/group", handlers.GroupService.GetGroupInfo)

	srv := &http.Server{
		Handler: g,
	}

	cleanup := func() {
		ctx, cancel := context.WithTimeout(context.Background(), time.Second*5)
		defer cancel()

		err := srv.Shutdown(ctx)
		if err != nil {
			log.Println("Server Shutdown", err)
		}
	}

	return srv, cleanup
}

type IsValid interface{ IsValid() error }
type Normalization interface{ Normalization() }
type Validator interface{ ValidRequest() error }

func handleGin[I, O any](gc *gin.Context, input I, f func(context.Context, I) (O, error)) {

	validate := validator.New()
	if err := validate.StructCtx(gc, input); err != nil {
		base.WriteError(gc, http.StatusBadRequest, "请求参数校验不通过："+err.Error())
		return
	}

	if i, ok := any(input).(Validator); ok {
		err := i.ValidRequest()
		if err != nil {
			base.WriteError(gc, http.StatusBadRequest, "请求参数校验不通过："+err.Error())
			return
		}
	}

	if i, ok := any(input).(Normalization); ok {
		i.Normalization()
	}

	output, err := f(gc, input)

	if err != nil {
		base.WriteError(gc, http.StatusInternalServerError, err.Error())
		return
	}

	base.WriteOk(gc, output)
}

func Route[I, O any](g gin.IRouter, method string, relativePath string, f func(context.Context, I) (O, error)) {
	g.Handle(method, relativePath, func(gc *gin.Context) {
		var input I
		if err := gc.ShouldBind(&input); err != nil {
			base.WriteError(gc, http.StatusBadRequest, "请求格式错误："+err.Error())
			return
		}

		handleGin(gc, input, f)
	})

	signedPath := "/open" + relativePath
	g.Handle(method, signedPath, func(gc *gin.Context) {
		var raw SignedRequest
		if err := gc.ShouldBind(&raw); err != nil {
			base.WriteError(gc, http.StatusBadRequest, "请求格式错误："+err.Error())
			return
		}

		var input I
		if err := raw.Decode(&input); err != nil {
			base.WriteError(gc, http.StatusBadRequest, "验证失败："+err.Error())
			return
		}

		handleGin(gc, input, f)
	})
}

type SignedRequest struct {
	Request string `json:"request"`
	Ts      int    `json:"ts"`
	Sign    string `json:"sign"`
}

const requestSignSecret = "3tb2Hr$fLpaTf9Q9KAf&GRI1H7#FnmBQ"

func (sr *SignedRequest) validSign() error {
	sign := sha1.Sum([]byte(sr.Request + requestSignSecret))
	if sr.Sign != fmt.Sprintf("%x", sign) {
		return errors.New("签名错误")
	}

	return nil
}

func (sr *SignedRequest) Decode(ptr any) error {
	if err := sr.validSign(); err != nil {
		return err
	}

	return sonic.UnmarshalString(sr.Request, ptr)
}

func SleepAPI(gc *gin.Context) {
	value := gc.Query("sleep")
	ms, _ := strconv.Atoi(value)
	time.Sleep(time.Millisecond * time.Duration(ms))
}
